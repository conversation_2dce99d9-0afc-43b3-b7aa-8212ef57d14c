<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .box {
            width: 200px;
            height: 200px;
            background-color: aquamarine;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            /* 初始状态 - 不可见且带有撕裂效果 */
            opacity: 0;
            filter: blur(10px) contrast(2) brightness(1.5);
            clip-path: polygon(
                0% 0%,
                15% 5%,
                25% 0%,
                40% 8%,
                55% 2%,
                70% 7%,
                85% 1%,
                100% 6%,
                95% 20%,
                100% 35%,
                92% 50%,
                100% 65%,
                94% 80%,
                100% 95%,
                85% 100%,
                70% 94%,
                55% 100%,
                40% 96%,
                25% 100%,
                15% 93%,
                0% 100%,
                5% 85%,
                0% 70%,
                8% 55%,
                2% 40%,
                7% 25%,
                1% 15%,
                6% 0%
            );

            /* 动画 */
            animation: tearIn 2s ease-out forwards;
        }

        @keyframes tearIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8) rotate(5deg);
                filter: blur(15px) contrast(3) brightness(2);
                clip-path: polygon(
                    0% 0%,
                    20% 10%,
                    30% 0%,
                    50% 15%,
                    60% 5%,
                    80% 12%,
                    90% 2%,
                    100% 18%,
                    85% 25%,
                    100% 45%,
                    88% 60%,
                    100% 75%,
                    82% 88%,
                    100% 100%,
                    80% 90%,
                    60% 100%,
                    50% 85%,
                    30% 100%,
                    20% 88%,
                    0% 100%,
                    15% 75%,
                    0% 60%,
                    12% 45%,
                    5% 25%,
                    18% 12%,
                    2% 8%,
                    15% 0%
                );
            }

            30% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(0.9) rotate(2deg);
                filter: blur(8px) contrast(2.5) brightness(1.8);
                clip-path: polygon(
                    0% 0%,
                    18% 7%,
                    28% 2%,
                    45% 10%,
                    58% 3%,
                    75% 9%,
                    88% 1%,
                    100% 12%,
                    90% 22%,
                    100% 38%,
                    90% 52%,
                    100% 68%,
                    88% 82%,
                    100% 98%,
                    82% 95%,
                    65% 100%,
                    52% 90%,
                    35% 100%,
                    22% 95%,
                    0% 100%,
                    10% 82%,
                    0% 68%,
                    10% 52%,
                    3% 38%,
                    12% 22%,
                    1% 12%,
                    10% 2%
                );
            }

            60% {
                opacity: 0.7;
                transform: translate(-50%, -50%) scale(0.95) rotate(1deg);
                filter: blur(4px) contrast(1.8) brightness(1.4);
                clip-path: polygon(
                    0% 0%,
                    16% 4%,
                    26% 1%,
                    42% 6%,
                    57% 2%,
                    72% 5%,
                    87% 1%,
                    100% 8%,
                    94% 18%,
                    100% 32%,
                    93% 48%,
                    100% 62%,
                    92% 78%,
                    100% 96%,
                    84% 98%,
                    68% 100%,
                    52% 94%,
                    36% 100%,
                    20% 98%,
                    0% 100%,
                    6% 84%,
                    0% 68%,
                    7% 52%,
                    2% 36%,
                    8% 20%,
                    1% 8%,
                    6% 1%
                );
            }

            85% {
                opacity: 0.9;
                transform: translate(-50%, -50%) scale(0.98) rotate(0.5deg);
                filter: blur(2px) contrast(1.3) brightness(1.1);
                clip-path: polygon(
                    0% 0%,
                    14% 2%,
                    24% 0%,
                    38% 3%,
                    56% 1%,
                    71% 3%,
                    86% 0%,
                    100% 4%,
                    96% 16%,
                    100% 29%,
                    95% 46%,
                    100% 58%,
                    94% 76%,
                    100% 98%,
                    86% 99%,
                    71% 100%,
                    56% 97%,
                    38% 100%,
                    24% 99%,
                    0% 100%,
                    4% 86%,
                    0% 71%,
                    5% 56%,
                    1% 38%,
                    4% 24%,
                    0% 14%,
                    3% 0%
                );
            }

            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
                filter: blur(0px) contrast(1) brightness(1);
                clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
            }
        }

        /* 添加一些额外的视觉效果 */
        .box::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.3), transparent);
            opacity: 0;
            animation: glitch 2s ease-out;
            pointer-events: none;
        }

        @keyframes glitch {
            0%, 100% { opacity: 0; }
            10%, 30%, 50% { opacity: 0.8; }
            20%, 40% { opacity: 0.2; }
        }
    </style>
</head>
<body>
    <div class="box"></div>
</body>
</html>