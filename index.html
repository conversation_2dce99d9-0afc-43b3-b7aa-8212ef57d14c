<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .box {
            width: 200px;
            height: 200px;
            background-color: aquamarine;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            /* 初始状态 */
            opacity: 0;

            /* 动画 */
            animation: tearIn 0.8s ease-out forwards;
        }

        /* 创建横向撕裂效果的伪元素 */
        .box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                transparent 0px,
                transparent 8px,
                rgba(0, 0, 0, 0.8) 8px,
                rgba(0, 0, 0, 0.8) 12px,
                transparent 12px,
                transparent 20px,
                rgba(0, 0, 0, 0.9) 20px,
                rgba(0, 0, 0, 0.9) 22px,
                transparent 22px,
                transparent 35px,
                rgba(0, 0, 0, 0.7) 35px,
                rgba(0, 0, 0, 0.7) 38px
            );
            opacity: 1;
            animation: glitchLines 0.8s ease-out forwards;
            z-index: 1;
        }

        /* 添加额外的撕裂线条 */
        .box::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                0deg,
                transparent 0px,
                transparent 15px,
                rgba(0, 0, 0, 1) 15px,
                rgba(0, 0, 0, 1) 17px,
                transparent 17px,
                transparent 28px,
                rgba(0, 0, 0, 0.8) 28px,
                rgba(0, 0, 0, 0.8) 31px,
                transparent 31px,
                transparent 45px,
                rgba(0, 0, 0, 0.9) 45px,
                rgba(0, 0, 0, 0.9) 47px
            );
            opacity: 1;
            animation: glitchLines2 0.8s ease-out forwards;
            z-index: 2;
        }

        @keyframes tearIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.95);
                filter: blur(3px);
            }

            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(0.98);
                filter: blur(1px);
            }

            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
                filter: blur(0px);
            }
        }

        /* 横向撕裂线条动画 */
        @keyframes glitchLines {
            0% {
                opacity: 1;
                transform: translateX(0px);
            }

            20% {
                opacity: 0.9;
                transform: translateX(-2px);
            }

            40% {
                opacity: 0.7;
                transform: translateX(1px);
            }

            60% {
                opacity: 0.5;
                transform: translateX(-1px);
            }

            80% {
                opacity: 0.3;
                transform: translateX(0px);
            }

            100% {
                opacity: 0;
                transform: translateX(0px);
            }
        }

        @keyframes glitchLines2 {
            0% {
                opacity: 1;
                transform: translateX(0px) scaleX(1);
            }

            15% {
                opacity: 0.8;
                transform: translateX(3px) scaleX(0.98);
            }

            30% {
                opacity: 0.6;
                transform: translateX(-2px) scaleX(1.02);
            }

            45% {
                opacity: 0.4;
                transform: translateX(1px) scaleX(0.99);
            }

            70% {
                opacity: 0.2;
                transform: translateX(-1px) scaleX(1.01);
            }

            100% {
                opacity: 0;
                transform: translateX(0px) scaleX(1);
            }
        }
    </style>
</head>
<body>
    <div class="box"></div>
</body>
</html>