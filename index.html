<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .box {
            width: 200px;
            height: 200px;
            background-color: aquamarine;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            /* 初始状态 */
            opacity: 0;

            /* 动画 */
            animation: tearIn 0.8s ease-out forwards;
        }

        /* 创建不规则横向撕裂效果 */
        .box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                0deg,
                transparent 0%,
                transparent 8%,
                rgba(0, 0, 0, 0.9) 8%,
                rgba(0, 0, 0, 0.9) 10%,
                transparent 10%,
                transparent 15%,
                rgba(0, 0, 0, 0.7) 15%,
                rgba(0, 0, 0, 0.7) 16%,
                transparent 16%,
                transparent 22%,
                rgba(0, 0, 0, 0.95) 22%,
                rgba(0, 0, 0, 0.95) 25%,
                transparent 25%,
                transparent 28%,
                rgba(0, 0, 0, 0.6) 28%,
                rgba(0, 0, 0, 0.6) 29%,
                transparent 29%,
                transparent 35%,
                rgba(0, 0, 0, 0.8) 35%,
                rgba(0, 0, 0, 0.8) 38%,
                transparent 38%,
                transparent 42%,
                rgba(0, 0, 0, 0.9) 42%,
                rgba(0, 0, 0, 0.9) 43%,
                transparent 43%,
                transparent 48%,
                rgba(0, 0, 0, 0.75) 48%,
                rgba(0, 0, 0, 0.75) 51%,
                transparent 51%,
                transparent 58%,
                rgba(0, 0, 0, 0.85) 58%,
                rgba(0, 0, 0, 0.85) 59%,
                transparent 59%,
                transparent 65%,
                rgba(0, 0, 0, 0.7) 65%,
                rgba(0, 0, 0, 0.7) 68%,
                transparent 68%,
                transparent 72%,
                rgba(0, 0, 0, 0.9) 72%,
                rgba(0, 0, 0, 0.9) 73%,
                transparent 73%,
                transparent 78%,
                rgba(0, 0, 0, 0.8) 78%,
                rgba(0, 0, 0, 0.8) 81%,
                transparent 81%,
                transparent 88%,
                rgba(0, 0, 0, 0.6) 88%,
                rgba(0, 0, 0, 0.6) 89%,
                transparent 89%,
                transparent 95%,
                rgba(0, 0, 0, 0.85) 95%,
                rgba(0, 0, 0, 0.85) 97%,
                transparent 97%
            );
            opacity: 1;
            animation: glitchLines 0.8s ease-out forwards;
            z-index: 1;
            mix-blend-mode: multiply;
        }

        /* 添加更多不规则撕裂线条 */
        .box::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                0deg,
                transparent 0%,
                transparent 5%,
                rgba(0, 0, 0, 0.8) 5%,
                rgba(0, 0, 0, 0.8) 7%,
                transparent 7%,
                transparent 12%,
                rgba(0, 0, 0, 0.9) 12%,
                rgba(0, 0, 0, 0.9) 13%,
                transparent 13%,
                transparent 18%,
                rgba(0, 0, 0, 0.65) 18%,
                rgba(0, 0, 0, 0.65) 21%,
                transparent 21%,
                transparent 26%,
                rgba(0, 0, 0, 0.95) 26%,
                rgba(0, 0, 0, 0.95) 27%,
                transparent 27%,
                transparent 32%,
                rgba(0, 0, 0, 0.7) 32%,
                rgba(0, 0, 0, 0.7) 35%,
                transparent 35%,
                transparent 40%,
                rgba(0, 0, 0, 0.85) 40%,
                rgba(0, 0, 0, 0.85) 41%,
                transparent 41%,
                transparent 46%,
                rgba(0, 0, 0, 0.75) 46%,
                rgba(0, 0, 0, 0.75) 49%,
                transparent 49%,
                transparent 55%,
                rgba(0, 0, 0, 0.9) 55%,
                rgba(0, 0, 0, 0.9) 56%,
                transparent 56%,
                transparent 62%,
                rgba(0, 0, 0, 0.8) 62%,
                rgba(0, 0, 0, 0.8) 65%,
                transparent 65%,
                transparent 70%,
                rgba(0, 0, 0, 0.6) 70%,
                rgba(0, 0, 0, 0.6) 71%,
                transparent 71%,
                transparent 76%,
                rgba(0, 0, 0, 0.85) 76%,
                rgba(0, 0, 0, 0.85) 79%,
                transparent 79%,
                transparent 84%,
                rgba(0, 0, 0, 0.9) 84%,
                rgba(0, 0, 0, 0.9) 85%,
                transparent 85%,
                transparent 90%,
                rgba(0, 0, 0, 0.75) 90%,
                rgba(0, 0, 0, 0.75) 93%,
                transparent 93%
            );
            opacity: 1;
            animation: glitchLines2 0.8s ease-out forwards;
            z-index: 2;
            mix-blend-mode: multiply;
        }

        @keyframes tearIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.95);
                filter: blur(3px);
            }

            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(0.98);
                filter: blur(1px);
            }

            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
                filter: blur(0px);
            }
        }

        /* 不规则撕裂线条动画 - 模拟声波效果 */
        @keyframes glitchLines {
            0% {
                opacity: 1;
                transform: translateX(0px) scaleY(1) skewX(0deg);
                filter: blur(0px);
            }

            8% {
                opacity: 0.95;
                transform: translateX(-3px) scaleY(0.98) skewX(1deg);
                filter: blur(0.5px);
            }

            15% {
                opacity: 0.9;
                transform: translateX(2px) scaleY(1.02) skewX(-0.5deg);
                filter: blur(0px);
            }

            25% {
                opacity: 0.85;
                transform: translateX(-1px) scaleY(0.96) skewX(1.5deg);
                filter: blur(1px);
            }

            35% {
                opacity: 0.75;
                transform: translateX(4px) scaleY(1.05) skewX(-1deg);
                filter: blur(0.5px);
            }

            45% {
                opacity: 0.65;
                transform: translateX(-2px) scaleY(0.94) skewX(0.8deg);
                filter: blur(1.5px);
            }

            55% {
                opacity: 0.5;
                transform: translateX(1px) scaleY(1.03) skewX(-1.2deg);
                filter: blur(1px);
            }

            65% {
                opacity: 0.4;
                transform: translateX(-3px) scaleY(0.97) skewX(0.6deg);
                filter: blur(2px);
            }

            75% {
                opacity: 0.25;
                transform: translateX(2px) scaleY(1.01) skewX(-0.4deg);
                filter: blur(1.5px);
            }

            85% {
                opacity: 0.15;
                transform: translateX(-1px) scaleY(0.99) skewX(0.2deg);
                filter: blur(2.5px);
            }

            100% {
                opacity: 0;
                transform: translateX(0px) scaleY(1) skewX(0deg);
                filter: blur(3px);
            }
        }

        @keyframes glitchLines2 {
            0% {
                opacity: 1;
                transform: translateX(0px) scaleY(1) scaleX(1) skewX(0deg);
                filter: blur(0px);
            }

            12% {
                opacity: 0.92;
                transform: translateX(4px) scaleY(1.04) scaleX(0.97) skewX(-1.2deg);
                filter: blur(0.8px);
            }

            22% {
                opacity: 0.88;
                transform: translateX(-2px) scaleY(0.95) scaleX(1.03) skewX(0.8deg);
                filter: blur(0.3px);
            }

            32% {
                opacity: 0.78;
                transform: translateX(3px) scaleY(1.06) scaleX(0.96) skewX(-1.5deg);
                filter: blur(1.2px);
            }

            42% {
                opacity: 0.68;
                transform: translateX(-4px) scaleY(0.93) scaleX(1.04) skewX(1.1deg);
                filter: blur(0.7px);
            }

            52% {
                opacity: 0.55;
                transform: translateX(1px) scaleY(1.02) scaleX(0.98) skewX(-0.7deg);
                filter: blur(1.8px);
            }

            62% {
                opacity: 0.42;
                transform: translateX(-3px) scaleY(0.97) scaleX(1.02) skewX(0.9deg);
                filter: blur(1.3px);
            }

            72% {
                opacity: 0.3;
                transform: translateX(2px) scaleY(1.01) scaleX(0.99) skewX(-0.5deg);
                filter: blur(2.1px);
            }

            82% {
                opacity: 0.18;
                transform: translateX(-1px) scaleY(0.99) scaleX(1.01) skewX(0.3deg);
                filter: blur(2.5px);
            }

            92% {
                opacity: 0.08;
                transform: translateX(1px) scaleY(1.005) scaleX(0.995) skewX(-0.1deg);
                filter: blur(3px);
            }

            100% {
                opacity: 0;
                transform: translateX(0px) scaleY(1) scaleX(1) skewX(0deg);
                filter: blur(3.5px);
            }
        }
    </style>
</head>
<body>
    <div class="box"></div>
</body>
</html>