<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            background-color: #000;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .box {
            width: 200px;
            height: 200px;
            background-color: aquamarine;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            /* 初始状态 */
            opacity: 0;

            /* 动画 */
            animation: tearIn 2s ease-out forwards;
        }

        /* 声波式撕裂效果 - 第一层 */
        .box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                /* 模拟声波的不规则横线 */
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 2%, transparent 4%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.7) 8%, transparent 12%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.95) 15%, transparent 16%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.6) 22%, transparent 28%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.85) 32%, transparent 35%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.8) 38%, transparent 39%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 45%, transparent 48%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.65) 52%, transparent 58%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.75) 62%, transparent 66%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 70%, transparent 71%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.8) 78%, transparent 82%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.7) 88%, transparent 92%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.85) 95%, transparent 98%);
            background-size:
                100% 3px, 100% 5px, 100% 1px, 100% 6px, 100% 3px, 100% 1px,
                100% 4px, 100% 7px, 100% 4px, 100% 2px, 100% 5px, 100% 3px, 100% 2px;
            background-position:
                0 8%, 0 15%, 0 22%, 0 28%, 0 35%, 0 42%,
                0 48%, 0 55%, 0 65%, 0 72%, 0 78%, 0 88%, 0 95%;
            background-repeat: no-repeat;
            opacity: 1;
            animation: waveGlitch1 2s ease-out forwards;
            z-index: 1;
        }

        /* 声波式撕裂效果 - 第二层 */
        .box::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                /* 更多不规则的声波线条 */
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.8) 5%, transparent 9%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 12%, transparent 13%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.65) 18%, transparent 25%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.95) 26%, transparent 27%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.7) 32%, transparent 37%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.85) 40%, transparent 41%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.75) 46%, transparent 52%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 55%, transparent 56%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.8) 62%, transparent 68%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.6) 70%, transparent 71%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.85) 76%, transparent 82%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.9) 84%, transparent 85%),
                linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.75) 90%, transparent 96%);
            background-size:
                100% 4px, 100% 1px, 100% 7px, 100% 1px, 100% 5px, 100% 1px,
                100% 6px, 100% 1px, 100% 6px, 100% 1px, 100% 6px, 100% 1px, 100% 6px;
            background-position:
                0 5%, 0 12%, 0 18%, 0 26%, 0 32%, 0 40%,
                0 46%, 0 55%, 0 62%, 0 70%, 0 76%, 0 84%, 0 90%;
            background-repeat: no-repeat;
            opacity: 1;
            animation: waveGlitch2 2s ease-out forwards;
            z-index: 2;
        }

        @keyframes tearIn {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.95);
                filter: blur(3px);
            }

            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(0.98);
                filter: blur(1px);
            }

            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
                filter: blur(0px);
            }
        }

        /* 声波式撕裂动画 - 第一层 */
        @keyframes waveGlitch1 {
            0% {
                opacity: 1;
                transform: scaleX(1) translateX(0px);
                filter: blur(0px);
            }

            5% {
                opacity: 0.95;
                transform: scaleX(0.98) translateX(-8px);
                filter: blur(0.5px);
            }

            12% {
                opacity: 0.9;
                transform: scaleX(1.03) translateX(5px);
                filter: blur(0.2px);
            }

            18% {
                opacity: 0.85;
                transform: scaleX(0.96) translateX(-12px);
                filter: blur(1px);
            }

            25% {
                opacity: 0.8;
                transform: scaleX(1.05) translateX(3px);
                filter: blur(0.3px);
            }

            32% {
                opacity: 0.75;
                transform: scaleX(0.94) translateX(-15px);
                filter: blur(1.5px);
            }

            40% {
                opacity: 0.65;
                transform: scaleX(1.08) translateX(7px);
                filter: blur(0.8px);
            }

            48% {
                opacity: 0.55;
                transform: scaleX(0.92) translateX(-18px);
                filter: blur(2px);
            }

            55% {
                opacity: 0.45;
                transform: scaleX(1.06) translateX(4px);
                filter: blur(1.2px);
            }

            65% {
                opacity: 0.35;
                transform: scaleX(0.95) translateX(-10px);
                filter: blur(2.5px);
            }

            75% {
                opacity: 0.25;
                transform: scaleX(1.02) translateX(2px);
                filter: blur(2px);
            }

            85% {
                opacity: 0.15;
                transform: scaleX(0.98) translateX(-5px);
                filter: blur(3px);
            }

            95% {
                opacity: 0.05;
                transform: scaleX(1.01) translateX(1px);
                filter: blur(3.5px);
            }

            100% {
                opacity: 0;
                transform: scaleX(1) translateX(0px);
                filter: blur(4px);
            }
        }

        /* 声波式撕裂动画 - 第二层 */
        @keyframes waveGlitch2 {
            0% {
                opacity: 1;
                transform: scaleX(1) translateX(0px);
                filter: blur(0px);
            }

            8% {
                opacity: 0.92;
                transform: scaleX(1.04) translateX(10px);
                filter: blur(0.3px);
            }

            15% {
                opacity: 0.88;
                transform: scaleX(0.95) translateX(-6px);
                filter: blur(0.7px);
            }

            22% {
                opacity: 0.82;
                transform: scaleX(1.07) translateX(14px);
                filter: blur(0.5px);
            }

            30% {
                opacity: 0.75;
                transform: scaleX(0.93) translateX(-9px);
                filter: blur(1.2px);
            }

            38% {
                opacity: 0.68;
                transform: scaleX(1.09) translateX(16px);
                filter: blur(0.8px);
            }

            45% {
                opacity: 0.58;
                transform: scaleX(0.91) translateX(-11px);
                filter: blur(1.8px);
            }

            52% {
                opacity: 0.48;
                transform: scaleX(1.05) translateX(8px);
                filter: blur(1.5px);
            }

            60% {
                opacity: 0.38;
                transform: scaleX(0.96) translateX(-7px);
                filter: blur(2.2px);
            }

            70% {
                opacity: 0.28;
                transform: scaleX(1.03) translateX(5px);
                filter: blur(2.5px);
            }

            80% {
                opacity: 0.18;
                transform: scaleX(0.98) translateX(-3px);
                filter: blur(3px);
            }

            90% {
                opacity: 0.08;
                transform: scaleX(1.01) translateX(2px);
                filter: blur(3.5px);
            }

            100% {
                opacity: 0;
                transform: scaleX(1) translateX(0px);
                filter: blur(4px);
            }
        }
    </style>
</head>
<body>
    <div class="box"></div>
</body>
</html>